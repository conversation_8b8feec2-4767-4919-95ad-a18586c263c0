Implement historical versioning for inquiry comments to track all changes to comment `body` and `images` when updates occur. Use the existing self-referencing versioning pattern already scaffolded in the `InquiryComment` model, following the same implementation approach as `lambdas/api/src/procedures/inquiries/update.ts`.

## 📋 Detailed Implementation Requirements

### 1. **Schema Verification** ✅
Confirm the existing Prisma schema supports versioning:
- `InquiryComment` model has `originalId` field and `versions` self-relation
- `InquiryCommentImage` properly links to `InquiryComment` via `commentId`
- No schema changes required

### 2. **Refactor Comment Update Logic** 🛠️
Transform `lambdas/api/src/procedures/inquiries/comments/update.ts` from direct mutation to versioning:

**Current behavior:** Mutates the original comment record directly
**Required behavior:**
- Create new comment version with updated content
- Set `originalId` to point to the original comment
- Preserve original comment as historical record
- Maintain all authorization and tenant isolation checks

### 3. **Image Versioning Integration** 🧼
Ensure image handling works correctly with versioning:
- Use the existing `categorizeImageChanges` utility for selective image updates
- Associate images with the new comment version (not the original)
- Preserve image history by linking to appropriate comment versions
- Maintain image audit trail across comment versions

### 4. **Query Adjustments** 🔍
Update comment retrieval logic to show only current versions:
- Add `originalId: null` filter to `lambdas/api/src/procedures/inquiries/comments/list.ts`
- Ensure frontend displays only the latest version of each comment
- Verify comment counting and pagination work with versioned data

## 🎯 Implementation Pattern Reference
Follow the exact versioning pattern from `lambdas/api/src/procedures/inquiries/update.ts`:
- Create new record with `originalId` pointing to the original
- Use transaction to ensure atomicity
- Maintain proper timestamps (`createdAt` for version, preserve original `createdAt`)
- Handle related entities (images) correctly in the new version

## ✅ Expected Deliverables

| Component | Requirement | Status |
|-----------|-------------|---------|
| **Schema Validation** | Confirm versioning capability via `originalId` | ✅ Ready |
| **Update Logic Refactor** | Transform direct mutation to version creation | 🛠️ Required |
| **Image Version Handling** | Associate images with correct comment version | 🧼 Required |
| **Query Filtering** | Add `originalId: null` to list queries | 🔍 Required |
| **Comprehensive Testing** | Validate versioning, images, authorization | 🧪 Required |
| **Documentation Update** | Update markdown with versioning implementation | 📝 Required |

The implementation should maintain backward compatibility while adding full audit trail capabilities for comment modifications.
