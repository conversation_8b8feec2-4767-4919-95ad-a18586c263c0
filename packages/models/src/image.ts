import { type BicycleEventType, s3TenantsPrefix } from 'common';
import { format } from 'date-fns';
import { z } from 'zod';

export const sampleImages = ['public/images/bicycle-zoom-out.jpg'] as const;
export const SampleImageSchema = z.enum(sampleImages);

export const publicImagePrefix = 'public/images' as const;
export const publicImageKey = <T extends string>(path: T) =>
  `${publicImagePrefix}/${path}` as const;

const ImageKeyArgSchema = z.object({
  tenantId: z.string(), // Tenant IDs are simple strings, not UUIDs (e.g., 'oec', 'shinagawa')
  date: z.date(),
  filename: z.string().regex(/^.+\..+$/),
});

export const CreateLogoImageKeyArgSchema = ImageKeyArgSchema.extend({
  userId: z.string().uuid(),
});
export type CreateLogoImageKeyArg = z.infer<typeof CreateLogoImageKeyArgSchema>;
export const createLogoImageKey = (arg: CreateLogoImageKeyArg) => {
  const { tenantId, userId, date, filename } = CreateLogoImageKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/logo/user-${userId}/${dateString}/${filename}` as const;
};

export const CreateAvatarImageKeyArgSchema = ImageKeyArgSchema.extend({
  userId: z.string().uuid(),
});
export type CreateAvatarImageKeyArg = z.infer<typeof CreateAvatarImageKeyArgSchema>;
export const createAvatarImageKey = (arg: CreateAvatarImageKeyArg) => {
  const { tenantId, userId, date, filename } = CreateAvatarImageKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/avatar/${userId}/${dateString}/${filename}` as const;
};

export const CreateTeamLogoImageKeyArgSchema = ImageKeyArgSchema.extend({
  teamId: z.string().uuid(),
  userId: z.string().uuid(),
});
export type CreateTeamLogoImageKeyArg = z.infer<typeof CreateTeamLogoImageKeyArgSchema>;
export const createTeamLogoImageKey = (arg: CreateTeamLogoImageKeyArg) => {
  const { tenantId, teamId, userId, date, filename } = CreateTeamLogoImageKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/team-logo/${teamId}/user-${userId}/${dateString}/${filename}` as const;
};

export const CreateBicycleImageKeyArgSchema = ImageKeyArgSchema.extend({
  bicycleId: z.string().uuid(),
  userId: z.string().uuid(),
});
export type CreateBicycleImageKeyArg = z.infer<typeof CreateBicycleImageKeyArgSchema>;
export const createBicycleImageKey = (arg: CreateBicycleImageKeyArg) => {
  const { tenantId, bicycleId, userId, date, filename } = CreateBicycleImageKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/bicycles/${bicycleId}/user-${userId}/images/${dateString}/${filename}` as const;
};

export const CreateBicycleMarkerImageKeyArgSchema = ImageKeyArgSchema.extend({
  markerId: z.string().uuid(),
  userId: z.string().uuid(),
});
export type CreateBicycleMarkerImageKeyArg = z.infer<typeof CreateBicycleMarkerImageKeyArgSchema>;
export const createBicycleMarkerImageKey = (arg: CreateBicycleMarkerImageKeyArg) => {
  const { tenantId, markerId, userId, date, filename } =
    CreateBicycleMarkerImageKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/bicycle-markers/${markerId}/user-${userId}/images/${dateString}/${filename}` as const;
};

export const CreateStorageImageKeyArgSchema = ImageKeyArgSchema.extend({
  storageId: z.string().uuid(),
  userId: z.string().uuid(),
});
export type CreateStorageImageKeyArg = z.infer<typeof CreateStorageImageKeyArgSchema>;
export const createStorageImageKey = (arg: CreateStorageImageKeyArg) => {
  const { tenantId, storageId, userId, date, filename } = CreateStorageImageKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/storages/${storageId}/user-${userId}/images/${dateString}/${filename}` as const;
};

const CreateBicycleStyleImageKeyArgSchema = ImageKeyArgSchema.extend({
  bicycleStyleId: z.string().uuid(),
  userId: z.string().uuid(),
});
type CreateBicycleStyleImageKeyArg = z.infer<typeof CreateBicycleStyleImageKeyArgSchema>;
export const createBicycleStyleImageKey = (arg: CreateBicycleStyleImageKeyArg) => {
  const { tenantId, bicycleStyleId, userId, date, filename } =
    CreateBicycleStyleImageKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/cycle-styles/${bicycleStyleId}/user-${userId}/images/${dateString}/${filename}` as const;
};

export const createStorageMapImageKey = (arg: CreateStorageImageKeyArg) => {
  const { tenantId, storageId, userId, date, filename } = CreateStorageImageKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/storages/${storageId}/user-${userId}/map-image/${dateString}/${filename}` as const;
};

export const CreateLandmarkImageKeyArgSchema = ImageKeyArgSchema.extend({
  landmarkId: z.string().uuid(),
  userId: z.string().uuid(),
});
export type CreateLandmarkImageKeyArg = z.infer<typeof CreateLandmarkImageKeyArgSchema>;
export const createLandmarkImageKey = (arg: CreateLandmarkImageKeyArg) => {
  const { tenantId, landmarkId, userId, date, filename } =
    CreateLandmarkImageKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/landmarks/${landmarkId}/user-${userId}/images/${dateString}/${filename}` as const;
};

const imageSettingTypes = [
  'edit',
  'mark',
  'find',
  'ensureAbandoned',
  'remove',
  'store',
  'returnToOwner',
] as const satisfies ('mark' | 'edit' | BicycleEventType)[];
export const ImageSettingTypeSchema = z.enum(imageSettingTypes);
export type ImageSettingType = z.infer<typeof ImageSettingTypeSchema>;
export const CreateGuideImageKeyArgSchema = ImageKeyArgSchema.extend({
  settingType: ImageSettingTypeSchema,
  userId: z.string().uuid(),
});
export type CreateGideImageKeyArg = z.infer<typeof CreateGuideImageKeyArgSchema>;
export const createGuideImageKey = (arg: CreateGideImageKeyArg) => {
  const { tenantId, settingType, userId, date, filename } = CreateGuideImageKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/${settingType}-settings/guide-images/user-${userId}/images/${dateString}/${filename}` as const;
};

export const CreateInquiryImagesKeyArgSchema = ImageKeyArgSchema.extend({
  inquiryId: z.string().uuid(),
  userId: z.string().uuid(),
});
export type CreateInquiryImagesKeyArg = z.infer<typeof CreateInquiryImagesKeyArgSchema>;
export const createInquiryImagesKey = (arg: CreateInquiryImagesKeyArg) => {
  const { tenantId, inquiryId, userId, date, filename } =
    CreateInquiryImagesKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/inquiries/${inquiryId}/user-${userId}/images/${dateString}/${filename}` as const;
};

export const CreateInquiryCommentImagesKeyArgSchema = ImageKeyArgSchema.extend({
  inquiryId: z.string().uuid(),
  commentId: z.string().uuid(),
  userId: z.string().uuid(),
});
export type CreateInquiryCommentImagesKeyArg = z.infer<
  typeof CreateInquiryCommentImagesKeyArgSchema
>;
export const createInquiryCommentImagesKey = (arg: CreateInquiryCommentImagesKeyArg) => {
  const { tenantId, inquiryId, commentId, userId, date, filename } =
    CreateInquiryCommentImagesKeyArgSchema.parse(arg);
  const dateString = format(date, 'yyyy-MM-dd-HH-mm-ss-SSS');
  return `${s3TenantsPrefix}/${tenantId}/inquiries/${inquiryId}/comments/${commentId}/user-${userId}/images/${dateString}/${filename}` as const;
};

export const isS3Key = (key: string) => key.startsWith(`${s3TenantsPrefix}/`);
export const s3KeyToUrl = (baseUrl: string, key: string): string => `${baseUrl}/${key}`;
/**
 * | 引数      | 説明                 |
 * | :-------- | :------------------- |
 * | データURL | そのまま             |
 * | S3キー    | フルパス             |
 */
export const enableUrl = (baseUrl: string, url: string): string =>
  isS3Key(url) ? s3KeyToUrl(baseUrl, url) : url;

export const takePhotoFilename = (nextIndex: number) => `image-${nextIndex}` as const;
export const takePhotoDescription = (nextIndex: number) => `${nextIndex}枚目` as const;
