import { z } from 'zod';

import type { Context, TRPCProcedure } from '../../types';

const InputSchema = z.object({ id: z.string() }).strict();
type Input = z.infer<typeof InputSchema>;
type QueryArgs = { input: Input; ctx: Context };

export const getInquiryQuery = async ({ input: { id }, ctx: { prisma, tenantId } }: QueryArgs) => {
  const { versions, comments, ...inquiry } = await prisma.inquiry.findUniqueOrThrow({
    where: { tenantId, id },
    include: {
      images: { orderBy: { sortOrder: 'asc' } },
      versions: {
        include: {
          images: { orderBy: { sortOrder: 'asc' } },
          assignUsers: { include: { user: true } },
        },
      },
      comments: {
        where: { originalId: null }, // Only show current versions, not historical ones
        include: { images: { orderBy: { sortOrder: 'asc' } } },
      },
      assignUsers: { include: { user: true } },
    },
  });
  return {
    ...inquiry,
    events: [...versions, ...comments].sort(
      (a, b) => a.createdAt.getTime() - b.createdAt.getTime(),
    ),
  };
};

export const getInquiry = (p: TRPCProcedure) => p.input(InputSchema).query(getInquiryQuery);
