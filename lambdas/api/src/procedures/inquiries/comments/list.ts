import { z } from 'zod';
import type { Context, TRPCProcedure } from '../../../types';

const InputSchema = z
  .object({
    inquiryId: z.string().uuid(),
  })
  .strict();

type Input = z.infer<typeof InputSchema>;
type QueryArgs = { input: Input; ctx: Context };

export const listInquiryCommentsQuery = async ({ input, ctx }: QueryArgs) => {
  const { prisma, tenantId } = ctx;
  return prisma.inquiryComment.findMany({
    where: {
      inquiryId: input.inquiryId,
      tenantId,
      deleted: false,
      originalId: null, // Only show current versions, not historical ones
    },
    include: {
      user: true,
      images: {
        where: { deleted: false },
        orderBy: { sortOrder: 'asc' },
      },
    },
    orderBy: { createdAt: 'asc' },
  });
};

export const listInquiryComments = (p: TRPCProcedure) =>
  p.input(InputSchema).query(listInquiryCommentsQuery);
